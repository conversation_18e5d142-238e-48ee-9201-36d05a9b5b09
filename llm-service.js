// llm-service.js
// Modular LLM service architecture with plugin support

/**
 * Simple template engine for replacing placeholders in strings
 */
class TemplateEngine {
  /**
   * Replace placeholders in a template string
   * @param {string} template - Template string with {{placeholder}} syntax
   * @param {Object} variables - Object with variable values
   * @returns {string} - Template with placeholders replaced
   */
  static render(template, variables = {}) {
    if (!template || typeof template !== 'string') {
      return template;
    }
    
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables.hasOwnProperty(key) ? variables[key] : match;
    });
  }
}

/**
 * Base LLM service interface
 */
class BaseLLMService {
  constructor(config) {
    this.config = config;
  }

  /**
   * Generate enhanced prompt using LLM
   * @param {string} previousPrompt - Previous scene prompt
   * @param {string} currentPrompt - Current scene prompt  
   * @param {string} nextPrompt - Next scene prompt
   * @param {string} interpolateMode - Interpolation mode (previous, next, both, none)
   * @returns {Promise<string>} - Enhanced prompt
   */
  async generatePrompt(previousPrompt, currentPrompt, nextPrompt, interpolateMode) {
    throw new Error('generatePrompt method must be implemented by subclass');
  }

  /**
   * Build context string based on interpolation mode
   * @param {string} previousPrompt - Previous scene prompt
   * @param {string} currentPrompt - Current scene prompt
   * @param {string} nextPrompt - Next scene prompt
   * @param {string} interpolateMode - Interpolation mode
   * @returns {string} - Context string
   */
  buildContext(previousPrompt, currentPrompt, nextPrompt, interpolateMode) {
    let contextParts = [];
    
    if (interpolateMode === "previous" || interpolateMode === "both") {
      if (previousPrompt) {
        contextParts.push(`Previous scene: "${previousPrompt}"`);
      }
    }

    contextParts.push(`Current scene: "${currentPrompt}"`);

    if (interpolateMode === "next" || interpolateMode === "both") {
      if (nextPrompt) {
        contextParts.push(`Next scene: "${nextPrompt}"`);
      }
    }

    return contextParts.join("\n");
  }
}

/**
 * OpenAI LLM service implementation
 */
class OpenAIService extends BaseLLMService {
  constructor(config, apiKey) {
    super(config);
    this.apiKey = apiKey;
    
    if (!this.apiKey) {
      throw new Error("OpenAI API key not found in environment variables");
    }
  }

  async generatePrompt(previousPrompt, currentPrompt, nextPrompt, interpolateMode) {
    const axios = (await import('axios')).default;
    
    const context = this.buildContext(previousPrompt, currentPrompt, nextPrompt, interpolateMode);
    
    // Render system prompt with context
    const systemPrompt = TemplateEngine.render(this.config.system_prompt, { context });
    
    try {
      const response = await axios.post(this.config.api_base, {
        model: this.config.model,
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: this.config.user_prompt }
        ],
        max_tokens: this.config.max_tokens,
        temperature: this.config.temperature
      }, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: this.config.timeout || 30000
      });

      const enhancedPrompt = response.data.choices[0]?.message?.content?.trim();
      if (!enhancedPrompt) {
        throw new Error("Empty response from OpenAI API");
      }

      return enhancedPrompt;
    } catch (error) {
      console.error("OpenAI API Error:", error.message);
      throw error;
    }
  }
}

/**
 * OpenRouter LLM service implementation
 */
class OpenRouterService extends BaseLLMService {
  constructor(config, apiKey) {
    super(config);
    this.apiKey = apiKey;
    
    if (!this.apiKey) {
      throw new Error("OpenRouter API key not found in environment variables");
    }
  }

  async generatePrompt(previousPrompt, currentPrompt, nextPrompt, interpolateMode) {
    const axios = (await import('axios')).default;
    
    const context = this.buildContext(previousPrompt, currentPrompt, nextPrompt, interpolateMode);
    
    // Render system prompt with context
    const systemPrompt = TemplateEngine.render(this.config.system_prompt, { context });
    
    try {
      const response = await axios.post(this.config.api_base, {
        model: this.config.model,
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: this.config.user_prompt }
        ],
        max_tokens: this.config.max_tokens,
        temperature: this.config.temperature
      }, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://github.com/bboldi/comfy_video_chain',
          'X-Title': 'ComfyUI Video Chain'
        },
        timeout: this.config.timeout || 30000
      });

      const enhancedPrompt = response.data.choices[0]?.message?.content?.trim();
      if (!enhancedPrompt) {
        throw new Error("Empty response from OpenRouter API");
      }

      return enhancedPrompt;
    } catch (error) {
      console.error("OpenRouter API Error:", error.message);
      throw error;
    }
  }
}

/**
 * LLM service factory
 */
class LLMServiceFactory {
  /**
   * Create LLM service instance based on configuration
   * @param {Object} config - LLM configuration
   * @param {string} apiKey - API key for the service
   * @returns {BaseLLMService} - LLM service instance
   */
  static create(config, apiKey) {
    switch (config.service.toLowerCase()) {
      case 'openai':
        return new OpenAIService(config, apiKey);
      case 'openrouter':
        return new OpenRouterService(config, apiKey);
      default:
        throw new Error(`Unsupported LLM service: ${config.service}`);
    }
  }
}

/**
 * Simple interpolation service for prompt templates
 */
class SimpleInterpolationService {
  constructor(templates) {
    this.templates = templates;
  }

  /**
   * Generate simple interpolation based on mode
   * @param {string} currentPrompt - Current prompt
   * @param {string} prevPrompt - Previous prompt
   * @param {string} nextPrompt - Next prompt
   * @param {string} mode - Interpolation mode
   * @returns {string} - Interpolated prompt
   */
  generateInterpolation(currentPrompt, prevPrompt, nextPrompt, mode) {
    let promptParts = [];

    switch (mode) {
      case "next":
        if (nextPrompt) {
          promptParts.push(TemplateEngine.render(this.templates.current_template, { prompt: currentPrompt }));
          promptParts.push(TemplateEngine.render(this.templates.next_template, { prompt: nextPrompt }));
        } else {
          promptParts.push(currentPrompt);
        }
        break;

      case "previous":
        if (prevPrompt) {
          promptParts.push(TemplateEngine.render(this.templates.previous_template, { prompt: prevPrompt }));
          promptParts.push(TemplateEngine.render(this.templates.current_template, { prompt: currentPrompt }));
        } else {
          promptParts.push(currentPrompt);
        }
        break;

      case "both":
        if (prevPrompt && nextPrompt) {
          promptParts.push(TemplateEngine.render(this.templates.previous_template, { prompt: prevPrompt }));
          promptParts.push(TemplateEngine.render(this.templates.current_template, { prompt: currentPrompt }));
          promptParts.push(TemplateEngine.render(this.templates.next_template, { prompt: nextPrompt }));
        } else if (prevPrompt) {
          promptParts.push(TemplateEngine.render(this.templates.previous_template, { prompt: prevPrompt }));
          promptParts.push(TemplateEngine.render(this.templates.current_template, { prompt: currentPrompt }));
        } else if (nextPrompt) {
          promptParts.push(TemplateEngine.render(this.templates.current_template, { prompt: currentPrompt }));
          promptParts.push(TemplateEngine.render(this.templates.next_template, { prompt: nextPrompt }));
        } else {
          promptParts.push(currentPrompt);
        }
        break;

      default:
        promptParts.push(currentPrompt);
    }

    return promptParts.join(", ");
  }
}

export { 
  TemplateEngine, 
  BaseLLMService, 
  OpenAIService, 
  OpenRouterService, 
  LLMServiceFactory, 
  SimpleInterpolationService 
};
