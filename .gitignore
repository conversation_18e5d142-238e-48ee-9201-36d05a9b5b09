# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# User configuration files (contains sensitive data)
config/
# Keep the base workflow file in root
!wan2.2_chain_base.json

# Output files
output/
*.mp4
*.avi
*.mov
*.mkv
*.webm

# Runtime/temporary files
runs/
temp/
tmp/
*.tmp
*.temp

# Image files (generated/temporary)
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff
*.webp

# Text files (generated)
*.txt
concat_list.txt

# Logs
logs/
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE/Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python (if you add Python scripts later)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Virtual environments
venv/
env/
ENV/

# Cache directories
.cache/
.npm/
.yarn/

# Coverage reports
coverage/
*.cover
.nyc_output

# Backup files
*.bak
*.backup
