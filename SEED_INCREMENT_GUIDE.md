# Seed Increment Feature Guide

## Overview

The `seed_increment` feature provides fine-grained control over visual variation between video segments in your ComfyUI video chain workflow. This feature was introduced to solve the duplicate frame issue while giving users control over how much variation occurs between segments.

## How It Works

Each video segment uses a unique seed calculated as:
```
segment_seed = base_seed + (segment_index * seed_increment)
```

Where:
- `base_seed` is the seed value from your configuration
- `segment_index` starts at 0 for the first segment
- `seed_increment` is the new configurable parameter

## Configuration

Add the `seed_increment` parameter to your JSON configuration file:

```json
{
  "seed": 1234567890,
  "seed_increment": 1,
  "other_config": "..."
}
```

## Seed Increment Values and Effects

### `seed_increment: 0`
- **Effect**: All segments use the same seed
- **Result**: Maximum similarity between segments, but may cause duplicate frames
- **Use case**: When you want very consistent visual style but different prompts
- **Warning**: May reintroduce the duplicate frame issue

### `seed_increment: 1` (Default)
- **Effect**: Each segment increments seed by 1
- **Result**: Minimal variation, good balance between consistency and uniqueness
- **Use case**: Most general use cases, maintains visual coherence
- **Seeds**: 1234567890, 1234567891, 1234567892, 1234567893...

### `seed_increment: 10-100`
- **Effect**: Moderate variation between segments
- **Result**: More diverse visual interpretations while maintaining story flow
- **Use case**: When you want noticeable style variations between scenes
- **Seeds**: 1234567890, 1234567990, 1234568090, 1234568190... (for increment=100)

### `seed_increment: 1000+`
- **Effect**: High variation between segments
- **Result**: Significantly different visual styles and interpretations
- **Use case**: Creative projects where dramatic style changes are desired
- **Seeds**: 1234567890, 1234568890, 1234569890, 1234570890... (for increment=1000)

### Negative Values
- **Effect**: Seeds decrease with each segment
- **Result**: Similar to positive increments but in reverse order
- **Use case**: Experimental workflows

## Example Configurations

### Minimal Variation (Default)
```json
{
  "seed": 1234567890,
  "seed_increment": 1
}
```

### High Variation
```json
{
  "seed": 1234567890,
  "seed_increment": 100
}
```

### Maximum Consistency (Risk of Duplicates)
```json
{
  "seed": 1234567890,
  "seed_increment": 0
}
```

## Test Configurations

The project includes several test configurations to demonstrate the feature:

- `config/test_continuity_fix.json` - Default increment (1)
- `config/test_high_variation.json` - High variation (100)
- `config/test_low_variation.json` - No variation (0)

## Backward Compatibility

If `seed_increment` is not specified in your configuration file, it defaults to 1, maintaining the fix for duplicate frames while preserving existing behavior.

## Recommendations

1. **Start with default (1)**: Good balance for most use cases
2. **Use 10-50**: For moderate style variations
3. **Use 100+**: For dramatic style changes
4. **Avoid 0**: Unless you specifically want identical seeds (may cause duplicates)
5. **Test different values**: Find the right balance for your specific content

## Troubleshooting

- **Duplicate frames returning**: Try increasing `seed_increment` from 0 to 1 or higher
- **Too much variation**: Reduce `seed_increment` to a smaller positive value
- **Inconsistent results**: Ensure `seed_increment` is a valid number
