#!/bin/bash

# ComfyUI Video Chain Setup Script

echo "🚀 Setting up ComfyUI Video Chain..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed"

# Set up environment file
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created"
    echo ""
    echo "⚠️  IMPORTANT: Edit .env file and add your API keys:"
    echo "   - OPENAI_API_KEY (for OpenAI service)"
    echo "   - OPENROUTER_API_KEY (for OpenRouter service)"
    echo ""
else
    echo "✅ .env file already exists"
fi

# Create config directory if it doesn't exist
if [ ! -d "config" ]; then
    echo "📁 Creating config directory..."
    mkdir -p config
    echo "✅ Config directory created"
fi

# Create output directory if it doesn't exist
if [ ! -d "output" ]; then
    echo "📁 Creating output directory..."
    mkdir -p output
    echo "✅ Output directory created"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file and add your API keys"
echo "2. Create a configuration file in config/ (see config/example.json)"
echo "3. Make sure ComfyUI is running"
echo "4. Run: node chain_i2v.js ./config/your_config.json"
echo ""
echo "📚 Documentation:"
echo "   - README.md - General usage"
echo "   - LLM_CONFIGURATION.md - LLM setup and configuration"
